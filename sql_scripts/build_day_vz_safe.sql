CREATE OR REPLACE FUNCTION build_day_vz(
    p_journey subscriber.content_sms.journey%TYPE,
    p_platform subscriber.transaction_sms.platform%TYPE
)
RETURNS TEXT AS
$$
DECLARE
    v_version subscriber.content_sms.version%TYPE;
    v_message subscriber.content_sms.message%TYPE;
    v_phone subscriber.subscriber_sms.customer_phone%TYPE;
    v_email subscriber.subscriber_sms.emailaddress%TYPE;
    v_firstname subscriber.subscriber_sms.firstname%TYPE;
    v_lastname subscriber.subscriber_sms.lastname%TYPE;
    v_shorturl subscriber.subscriber_sms.shorturl%TYPE;
    v_pl_shorturl subscriber.subscriber_sms.pl_shorturl%TYPE;
    v_zip subscriber.subscriber_sms.zip%TYPE;
    v_prop_count subscriber.zip.prop_count%TYPE;
    v_city subscriber.zip.city%TYPE;
    v_brand_name subscriber.brand.brand_name%TYPE;
    v_vertical subscriber.brand.vertical%TYPE;
    v_count INT := 0;
    v_final_message TEXT;
    v_lock_acquired BOOLEAN := FALSE;
    v_lock_key BIGINT := 123456791; -- Unique key for build_day_vz_safe function

BEGIN
    -- Check if the same script is already running using advisory lock
    -- This prevents multiple instances of the function from running simultaneously
    SELECT pg_try_advisory_lock(v_lock_key) INTO v_lock_acquired;
    
    IF NOT v_lock_acquired THEN
        RETURN 'Script is already running. Exiting to prevent duplicate execution.';
    END IF;

    -- Main processing logic starts here
    FOR v_phone, v_email, v_firstname, v_lastname, v_shorturl, v_zip, v_brand_name, v_vertical, v_pl_shorturl IN
        SELECT s.customer_phone, s.emailaddress, s.firstname, s.lastname, s.shorturl, s.zip, b.brand_name, b.vertical, s.pl_shorturl
        FROM subscriber.subscriber_sms s
        LEFT JOIN subscriber.brand b ON s.brand_id = b.brand_id
        LEFT JOIN subscriber.optout_sms o ON s.customer_phone = o.phone
        LEFT JOIN (SELECT phone FROM subscriber.transaction_sms WHERE campaign_date = CURRENT_DATE) t ON s.customer_phone = t.phone
        WHERE o.phone IS NULL
            AND t.phone IS NULL
            AND s.Blacklisted <> 'true'
            AND s.carrierparent LIKE '%VERIZON%'
            AND s.status <> 'not_mobile'
            AND s.adid NOT LIKE 'CCS440811%'
            AND s.original_adid NOT LIKE 'CCS440811%'
            AND s.shorturl IS NOT NULL
            AND s.leaddate >= date_trunc('day', now()) + interval '1 hours'
            AND s.leaddate <= date_trunc('day', now()) + interval '10 hours'
    LOOP
        SELECT scs.version, scs.message
        INTO v_version, v_message
        FROM subscriber.content_sms scs
        WHERE scs.vertical = v_vertical
          AND scs.journey = p_journey
        ORDER BY RANDOM()
        LIMIT 1;

        SELECT z.prop_count, z.city
        INTO v_prop_count, v_city
        FROM subscriber.zip z
        WHERE z.zip = v_zip;

        IF v_vertical = 'CRC' AND v_pl_shorturl IS NOT NULL THEN
            v_shorturl := v_pl_shorturl;
        END IF;

        v_final_message := v_message;
        v_final_message := REPLACE(v_final_message, '{{firstname}}', v_firstname);
        v_final_message := REPLACE(v_final_message, '{{lastname}}', v_lastname);
        v_final_message := REPLACE(v_final_message, '{{email}}', v_email);
        v_final_message := REPLACE(v_final_message, '{{zip}}', v_zip);
        v_final_message := REPLACE(v_final_message, '{{prop_count}}', CASE WHEN v_prop_count = 0 THEN 'newly available' ELSE v_prop_count::TEXT END);
        v_final_message := REPLACE(v_final_message, '{{city}}', v_city);
        v_final_message := REPLACE(v_final_message, '{{brand_name}}', v_brand_name);
        v_final_message := REPLACE(v_final_message, '{{weekday}}', to_char(CURRENT_DATE, 'Day'));
        v_final_message := REPLACE(v_final_message, '{{date}}', to_char(CURRENT_DATE, 'MM/DD/YYYY'));

        INSERT INTO subscriber.transaction_sms(phone, email, firstname, lastname, version, message, platform, campaign_date)
        VALUES (
            v_phone,
            v_email,
            v_firstname,
            v_lastname,
            v_version,
            CONCAT(v_final_message, ' ', v_shorturl, '?A=', v_version,'&subid4=', to_hex(CAST(EXTRACT(EPOCH FROM CURRENT_DATE) AS INTEGER)), ' Reply STOP to unsubscribe.'),
            CASE
                WHEN p_platform = 'mce' THEN 'mce_vz'
                ELSE p_platform
            END,
            CURRENT_DATE
        );

        v_count := v_count + 1;
    END LOOP;

    -- Release the advisory lock before returning
    PERFORM pg_advisory_unlock(v_lock_key);
    
    RETURN CONCAT('Successfully inserted ', v_count, ' row(s) into transaction_sms.');

EXCEPTION
    WHEN OTHERS THEN
        -- Make sure to release the lock even if an error occurs
        PERFORM pg_advisory_unlock(v_lock_key);
        RETURN CONCAT('Error: ', SQLERRM);
END;
$$
LANGUAGE plpgsql;
